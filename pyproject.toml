[project]
name = "audit-review-processer"
version = "0.1.0"
description = "Audit Review Processer"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "boto3==1.38.29",
    "fastapi==0.115.12",
    "httpx==0.28.1",
    "langchain[google-genai]==0.3.25",
    "langchain-community==0.3.25",
    "langchain-google-genai==2.0.10",
    "pandas==2.2.3",
    "pydantic-settings==2.9.1",
    "uvicorn==0.34.2",
    "pydantic==2.11.5",
    "celery[redis]==5.5.3",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.14.1",
    "ruff>=0.12.0",
]
