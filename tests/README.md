# Testing Documentation

This directory contains comprehensive unit tests for the audit-review-processor project.

## Test Structure

```
tests/
├── __init__.py
├── conftest.py                           # Shared fixtures and test configuration
├── domain/                               # Domain layer tests
│   ├── __init__.py
│   ├── test_aom.py                      # Tests for AOMProcessing class
│   ├── test_guest.py                    # Tests for GuestProcessing class
│   └── common/
│       ├── __init__.py
│       └── test_utils.py                # Tests for domain utility functions
└── application/                         # Application layer tests
    ├── __init__.py
    ├── test_utils.py                    # Tests for application utility functions
    └── command_handlers/
        ├── __init__.py
        └── test_process_data.py         # Tests for ProcessDataHandler class
```

## Running Tests

### Prerequisites

Ensure you have installed the testing dependencies:

```bash
uv add --group dev pytest pytest-mock pytest-asyncio pytest-cov
```

### Run All Tests

```bash
# Using pytest directly
pytest

# Using the test runner script
python run_tests.py

# Using uv
uv run pytest
```

### Run Specific Test Files

```bash
# Test domain utilities
pytest tests/domain/common/test_utils.py

# Test AOM processing
pytest tests/domain/test_aom.py

# Test guest processing
pytest tests/domain/test_guest.py

# Test application utilities
pytest tests/application/test_utils.py

# Test process data handler
pytest tests/application/command_handlers/test_process_data.py
```

### Run Tests with Coverage

```bash
pytest --cov=taskify --cov-report=html --cov-report=term-missing
```

### Run Tests by Markers

```bash
# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Skip slow tests
pytest -m "not slow"
```

## Test Coverage

The test suite aims for 80%+ code coverage. Coverage reports are generated in:
- Terminal output (with `--cov-report=term-missing`)
- HTML report in `htmlcov/index.html` (with `--cov-report=html`)

## Test Categories

### Domain Layer Tests

#### `test_utils.py`
Tests for utility functions in `taskify/domain/common/utils.py`:
- `is_datetime()` - Date validation
- `is_number()` - Number validation  
- `is_url()` - URL validation
- `clean_string()` - String cleaning

#### `test_aom.py`
Tests for `AOMProcessing` class in `taskify/domain/aom.py`:
- `_is_valid_value()` - Value validation logic
- `process()` - AOM data processing workflow

#### `test_guest.py`
Tests for `GuestProcessing` class in `taskify/domain/guest.py`:
- `process()` - Guest review processing workflow

### Application Layer Tests

#### `test_utils.py`
Tests for utility functions in `taskify/application/utils.py`:
- `clean_list_fields()` - List field cleaning
- `convert_to_csv()` - CSV conversion functionality

#### `test_process_data.py`
Tests for `ProcessDataHandler` class in `taskify/application/command_handlers/process_data.py`:
- `handle()` - Main request handling
- `handle_process_data()` - Data processing workflow
- `get_issue_list()` - Issue extraction from data sources
- `shuffle_list_with_limit()` - List sampling functionality

## Mocking Strategy

The tests use comprehensive mocking for external dependencies:

- **AWS Services**: S3 operations, file uploads/downloads
- **AI Services**: Gemini client responses
- **Email Services**: Email sending functionality
- **Database Operations**: Data retrieval and storage
- **File System**: Temporary file operations

## Fixtures

Common test fixtures are defined in `conftest.py`:

- `sample_aom_dataframe` - Sample AOM data for testing
- `sample_guest_dataframe` - Sample guest review data
- `sample_process_data_request` - Sample request payload
- `mock_aws_client` - Mocked AWS client
- `mock_ai_client` - Mocked AI client
- `mock_email_client` - Mocked email client
- `temp_directory` - Temporary directory for file operations

## Best Practices

1. **Isolation**: Each test is independent and doesn't rely on external services
2. **Mocking**: External dependencies are mocked to ensure fast, reliable tests
3. **Coverage**: Tests cover both positive and negative scenarios
4. **Edge Cases**: Tests include edge cases and error conditions
5. **Descriptive Names**: Test method names clearly describe what is being tested
6. **Fixtures**: Reusable test data is defined in fixtures
7. **Assertions**: Tests use specific assertions to verify expected behavior

## Continuous Integration

These tests are designed to run in CI/CD pipelines with:
- Fast execution (no external dependencies)
- Reliable results (deterministic)
- Clear failure reporting
- Coverage metrics

## Adding New Tests

When adding new functionality:

1. Create corresponding test files in the appropriate directory
2. Follow the existing naming conventions (`test_*.py`)
3. Use appropriate fixtures from `conftest.py`
4. Mock external dependencies
5. Test both success and failure scenarios
6. Maintain or improve code coverage
