"""Unit tests for Guest processing domain logic."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch

from taskify.domain.guest import GuestProcessing


class TestGuestProcessing:
    """Test cases for GuestProcessing class."""

    def test_process_with_valid_data(self, guest_processing, sample_guest_dataframe):
        """Test process method with valid guest data."""
        result = guest_processing.process(sample_guest_dataframe)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Check structure of returned issues
        for issue in result:
            assert "issue" in issue
            assert "hotel_name" in issue
            assert isinstance(issue["issue"], str)
            assert isinstance(issue["hotel_name"], str)

    def test_process_with_empty_dataframe(self, guest_processing):
        """Test process method with empty DataFrame."""
        empty_df = pd.DataFrame()
        result = guest_processing.process(empty_df)
        
        assert isinstance(result, list)
        assert len(result) == 0

    def test_process_deduplicates_identical_reviews(self, guest_processing):
        """Test that process method deduplicates identical reviews for same hotel."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel A', 'Hotel B', 'Hotel A'],
            'review': ['Great service', 'Great service', 'Poor service', 'Different review']
        })
        
        result = guest_processing.process(df)
        
        # Should have 3 unique combinations: 
        # (Great service, Hotel A), (Poor service, Hotel B), (Different review, Hotel A)
        assert len(result) == 3
        
        # Check that duplicate (Great service, Hotel A) is removed
        hotel_a_reviews = [item["issue"] for item in result if item["hotel_name"] == "Hotel A"]
        assert len(hotel_a_reviews) == 2
        assert "Great service" in hotel_a_reviews
        assert "Different review" in hotel_a_reviews

    def test_process_handles_different_hotels_same_review(self, guest_processing):
        """Test that same review for different hotels are kept separate."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel B', 'Hotel C'],
            'review': ['Great service', 'Great service', 'Great service']
        })
        
        result = guest_processing.process(df)
        
        # Should keep all three as they are for different hotels
        assert len(result) == 3
        
        hotel_names = [item["hotel_name"] for item in result]
        assert "Hotel A" in hotel_names
        assert "Hotel B" in hotel_names
        assert "Hotel C" in hotel_names

    def test_process_with_nan_values(self, guest_processing):
        """Test process method handles NaN values correctly."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel B', 'Hotel C'],
            'review': ['Valid review', np.nan, 'Another review']
        })
        
        result = guest_processing.process(df)
        
        # Should skip the NaN review
        assert len(result) == 2
        reviews = [item["issue"] for item in result]
        assert "Valid review" in reviews
        assert "Another review" in reviews

    def test_process_with_empty_reviews(self, guest_processing):
        """Test process method handles empty review strings."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel B', 'Hotel C'],
            'review': ['Valid review', '', 'Another review']
        })
        
        result = guest_processing.process(df)
        
        # Should skip the empty review
        assert len(result) == 2
        reviews = [item["issue"] for item in result]
        assert "Valid review" in reviews
        assert "Another review" in reviews

    def test_process_with_whitespace_only_reviews(self, guest_processing):
        """Test process method handles whitespace-only reviews."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel B', 'Hotel C'],
            'review': ['Valid review', '   ', 'Another review']
        })
        
        result = guest_processing.process(df)
        
        # Should skip the whitespace-only review (after clean_string returns None)
        assert len(result) == 2
        reviews = [item["issue"] for item in result]
        assert "Valid review" in reviews
        assert "Another review" in reviews

    def test_process_cleans_hotel_names_and_reviews(self, guest_processing):
        """Test that process method cleans hotel names and reviews."""
        df = pd.DataFrame({
            'hotel': ['  Hotel A  \n', '\tHotel B\r\n'],
            'review': ['  Great service  \n', '\tPoor service\r']
        })
        
        result = guest_processing.process(df)
        
        assert len(result) == 2
        
        # Check that hotel names and reviews are cleaned
        for item in result:
            assert item["hotel_name"] in ["Hotel A", "Hotel B"]
            assert item["issue"] in ["Great service", "Poor service"]
            # Ensure no extra whitespace
            assert not item["hotel_name"].startswith(" ")
            assert not item["hotel_name"].endswith(" ")
            assert not item["issue"].startswith(" ")
            assert not item["issue"].endswith(" ")

    @patch('taskify.domain.guest.clean_string')
    def test_process_calls_clean_string(self, mock_clean_string, guest_processing):
        """Test that process method calls clean_string for hotel names and reviews."""
        # Set up mock to return cleaned values
        mock_clean_string.side_effect = lambda x: str(x).strip() if x else None
        
        df = pd.DataFrame({
            'hotel': ['Hotel A'],
            'review': ['Great service']
        })
        
        result = guest_processing.process(df)
        
        # Should call clean_string for both hotel name and review
        assert mock_clean_string.call_count == 2

    def test_process_with_none_values(self, guest_processing):
        """Test process method handles None values correctly."""
        df = pd.DataFrame({
            'hotel': ['Hotel A', None, 'Hotel C'],
            'review': ['Valid review', 'Another review', None]
        })
        
        # Replace None with np.nan as pandas typically does
        df = df.replace({None: np.nan})
        
        result = guest_processing.process(df)
        
        # Should only process the first row (Hotel A with Valid review)
        # Second row has None hotel, third row has None review
        assert len(result) == 1
        assert result[0]["hotel_name"] == "Hotel A"
        assert result[0]["issue"] == "Valid review"

    def test_process_maintains_correct_structure(self, guest_processing):
        """Test that process method returns correctly structured data."""
        df = pd.DataFrame({
            'hotel': ['Hotel Test'],
            'review': ['Test review']
        })
        
        result = guest_processing.process(df)
        
        assert len(result) == 1
        issue = result[0]
        
        # Check exact structure
        assert set(issue.keys()) == {"issue", "hotel_name"}
        assert issue["issue"] == "Test review"
        assert issue["hotel_name"] == "Hotel Test"

    def test_process_with_missing_columns(self, guest_processing):
        """Test process method when required columns are missing."""
        # Missing 'hotel' column
        df_missing_hotel = pd.DataFrame({
            'review': ['Test review']
        })
        
        with pytest.raises(KeyError):
            guest_processing.process(df_missing_hotel)
        
        # Missing 'review' column
        df_missing_review = pd.DataFrame({
            'hotel': ['Hotel A']
        })
        
        with pytest.raises(KeyError):
            guest_processing.process(df_missing_review)
