"""Unit tests for AOM processing domain logic."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, Mock

from taskify.domain.aom import AOMProcessing


class TestAOMProcessing:
    """Test cases for AOMProcessing class."""

    def test_is_valid_value_with_numbers(self, aom_processing):
        """Test _is_valid_value with numeric values."""
        assert aom_processing._is_valid_value("123") is True
        assert aom_processing._is_valid_value("123.45") is True
        assert aom_processing._is_valid_value("-123") is True
        assert aom_processing._is_valid_value("1e5") is True

    def test_is_valid_value_with_urls(self, aom_processing):
        """Test _is_valid_value with URL values."""
        assert aom_processing._is_valid_value("https://example.com") is True
        assert aom_processing._is_valid_value("http://test.com") is True
        assert aom_processing._is_valid_value("ftp://ftp.example.com") is True

    def test_is_valid_value_with_dates(self, aom_processing):
        """Test _is_valid_value with date values."""
        assert aom_processing._is_valid_value("2023-01-01") is True
        assert aom_processing._is_valid_value("January 1, 2023") is True
        assert aom_processing._is_valid_value("01/01/2023") is True

    def test_is_valid_value_with_invalid_values(self, aom_processing):
        """Test _is_valid_value with invalid values."""
        assert aom_processing._is_valid_value("invalid_text") is False
        assert aom_processing._is_valid_value("random string") is False
        assert aom_processing._is_valid_value("not_a_url_or_number") is False

    def test_is_valid_value_with_edge_cases(self, aom_processing):
        """Test _is_valid_value with edge cases."""
        assert aom_processing._is_valid_value("") is False
        assert aom_processing._is_valid_value("   ") is False

    def test_process_with_valid_data(self, aom_processing, sample_aom_dataframe):
        """Test process method with valid AOM data."""
        result = aom_processing.process(sample_aom_dataframe)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Check structure of returned issues
        for issue in result:
            assert "issue" in issue
            assert "hotel_name" in issue
            assert isinstance(issue["issue"], str)
            assert isinstance(issue["hotel_name"], str)

    def test_process_with_empty_dataframe(self, aom_processing):
        """Test process method with empty DataFrame."""
        empty_df = pd.DataFrame()
        result = aom_processing.process(empty_df)
        
        assert isinstance(result, list)
        assert len(result) == 0

    def test_process_excludes_common_columns(self, aom_processing):
        """Test that process method excludes common and excluded columns."""
        df = pd.DataFrame({
            'Date': ['2023-01-01'],
            'Site name': ['Hotel A'],
            'Name': ['John Doe'],
            'Email address': ['<EMAIL>'],
            'Created At': ['2023-01-01 10:00:00'],
            'Created By': ['Admin'],
            'Updated At': ['2023-01-01 11:00:00'],
            'Updated By': ['Admin'],
            'Issue Field': ['invalid_issue']  # This should be processed
        })
        
        result = aom_processing.process(df)
        
        # Should only process 'Issue Field' and find it invalid
        assert len(result) == 1
        assert "Issue Field" in result[0]["issue"]

    def test_process_with_nan_values(self, aom_processing):
        """Test process method handles NaN values correctly."""
        df = pd.DataFrame({
            'Site name': ['Hotel A', 'Hotel B'],
            'Issue Field': ['invalid_issue', np.nan],
            'Another Field': [np.nan, 'another_invalid']
        })
        
        result = aom_processing.process(df)
        
        # Should only process non-NaN values
        assert len(result) == 2
        issue_texts = [issue["issue"] for issue in result]
        assert any("Issue Field" in text for text in issue_texts)
        assert any("Another Field" in text for text in issue_texts)

    def test_process_deduplicates_issues(self, aom_processing):
        """Test that process method deduplicates identical issues."""
        df = pd.DataFrame({
            'Site name': ['Hotel A', 'Hotel A', 'Hotel B'],
            'Issue Field': ['same_invalid', 'same_invalid', 'different_invalid']
        })
        
        result = aom_processing.process(df)
        
        # Should deduplicate the identical issue from Hotel A
        assert len(result) == 2
        
        # Check that we have one issue for Hotel A and one for Hotel B
        hotel_names = [issue["hotel_name"] for issue in result]
        assert "Hotel A" in hotel_names
        assert "Hotel B" in hotel_names

    def test_process_with_valid_values_only(self, aom_processing):
        """Test process method when all values are valid."""
        df = pd.DataFrame({
            'Site name': ['Hotel A', 'Hotel B'],
            'Number Field': ['123', '456'],
            'URL Field': ['https://example.com', 'http://test.com'],
            'Date Field': ['2023-01-01', '2023-01-02']
        })
        
        result = aom_processing.process(df)
        
        # Should return empty list as all values are valid
        assert len(result) == 0

    def test_process_formats_issue_correctly(self, aom_processing):
        """Test that process method formats issues correctly."""
        df = pd.DataFrame({
            'Site name': ['Hotel Test'],
            'Problem Field': ['invalid issue']
        })
        
        result = aom_processing.process(df)
        
        assert len(result) == 1
        issue = result[0]
        
        # Check issue format: "column:value"
        assert ":" in issue["issue"]
        assert "Problem Field" in issue["issue"]
        assert "invalid issue" in issue["issue"]
        assert issue["hotel_name"] == "Hotel Test"

    @patch('taskify.domain.aom.clean_string')
    def test_process_calls_clean_string(self, mock_clean_string, aom_processing):
        """Test that process method calls clean_string for formatting."""
        mock_clean_string.side_effect = lambda x: x  # Return input unchanged
        
        df = pd.DataFrame({
            'Site name': ['Hotel A'],
            'Issue Field': ['invalid_issue']
        })
        
        aom_processing.process(df)
        
        # Should call clean_string for column name, value, and hotel name
        assert mock_clean_string.call_count >= 3

    def test_process_with_missing_site_name(self, aom_processing):
        """Test process method when Site name column is missing."""
        df = pd.DataFrame({
            'Issue Field': ['invalid_issue']
        })
        
        # Should handle missing Site name gracefully
        with pytest.raises(KeyError):
            aom_processing.process(df)
