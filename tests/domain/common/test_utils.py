"""Unit tests for domain common utility functions."""

import pytest
from taskify.domain.common.utils import is_datetime, is_number, is_url, clean_string


class TestIsDatetime:
    """Test cases for is_datetime function."""

    def test_is_datetime_with_valid_iso_format(self):
        """Test is_datetime with valid ISO format dates."""
        assert is_datetime("2023-01-01") is True
        assert is_datetime("2023-12-31") is True
        assert is_datetime("2023-01-01T10:30:00") is True

    def test_is_datetime_with_valid_common_formats(self):
        """Test is_datetime with valid common date formats."""
        assert is_datetime("01/01/2023") is True
        assert is_datetime("January 1, 2023") is True
        assert is_datetime("Jan 1, 2023") is True
        assert is_datetime("2023-01-01 10:30:00") is True

    def test_is_datetime_with_invalid_dates(self):
        """Test is_datetime with invalid date strings."""
        assert is_datetime("invalid_date") is False
        assert is_datetime("2023-13-01") is False  # Invalid month
        assert is_datetime("2023-01-32") is False  # Invalid day
        assert is_datetime("not a date") is False
        assert is_datetime("") is False

    def test_is_datetime_with_non_string_input(self):
        """Test is_datetime with non-string inputs."""
        assert is_datetime(123) is False
        assert is_datetime(None) is False
        assert is_datetime([]) is False
        assert is_datetime({}) is False

    def test_is_datetime_with_edge_cases(self):
        """Test is_datetime with edge cases."""
        assert is_datetime("2023") is True  # Year only - dateutil.parser can parse this
        assert is_datetime("01") is True  # Day only - dateutil.parser can parse this too
        assert is_datetime("2023-01") is True  # Year-month only - dateutil.parser can parse this


class TestIsNumber:
    """Test cases for is_number function."""

    def test_is_number_with_valid_integers(self):
        """Test is_number with valid integer strings."""
        assert is_number("123") is True
        assert is_number("0") is True
        assert is_number("-123") is True
        assert is_number("+123") is False  # Positive sign not supported by regex

    def test_is_number_with_valid_floats(self):
        """Test is_number with valid float strings."""
        assert is_number("123.45") is True
        assert is_number("0.0") is True
        assert is_number("-123.45") is True
        assert is_number(".5") is False  # Leading decimal not supported

    def test_is_number_with_scientific_notation(self):
        """Test is_number with scientific notation."""
        assert is_number("1e5") is True
        assert is_number("1E5") is True
        assert is_number("1.23e-4") is True
        assert is_number("-1.23E+4") is True

    def test_is_number_with_invalid_numbers(self):
        """Test is_number with invalid number strings."""
        assert is_number("abc") is False
        assert is_number("12.34.56") is False
        assert is_number("12a") is False
        assert is_number("") is False
        assert is_number(" ") is False

    def test_is_number_with_edge_cases(self):
        """Test is_number with edge cases."""
        assert is_number("123.") is False  # Trailing decimal
        assert is_number(".") is False  # Just decimal point
        assert is_number("e5") is False  # Missing base number


class TestIsUrl:
    """Test cases for is_url function."""

    def test_is_url_with_valid_http_urls(self):
        """Test is_url with valid HTTP URLs."""
        assert is_url("http://example.com") is True
        assert is_url("https://example.com") is True
        assert is_url("http://www.example.com") is True
        assert is_url("https://subdomain.example.com") is True

    def test_is_url_with_valid_urls_with_paths(self):
        """Test is_url with valid URLs containing paths."""
        assert is_url("https://example.com/path") is True
        assert is_url("https://example.com/path/to/resource") is True
        assert is_url("https://example.com/path?query=value") is True
        assert is_url("https://example.com:8080/path") is True

    def test_is_url_with_ftp_urls(self):
        """Test is_url with FTP URLs."""
        assert is_url("ftp://ftp.example.com") is True
        assert is_url("ftp://ftp.example.com/file.txt") is True

    def test_is_url_with_invalid_urls(self):
        """Test is_url with invalid URLs."""
        assert is_url("not_a_url") is False
        assert is_url("example.com") is False  # Missing scheme
        assert is_url("http://") is False  # Missing netloc
        assert is_url("://example.com") is False  # Missing scheme
        assert is_url("") is False

    def test_is_url_with_non_string_input(self):
        """Test is_url with non-string inputs."""
        assert is_url(123) is False
        assert is_url(None) is False
        assert is_url([]) is False
        assert is_url({}) is False

    def test_is_url_with_strict_mode(self):
        """Test is_url with strict mode enabled."""
        # Valid URLs that should pass strict regex
        assert is_url("https://example.com", strict=True) is True
        assert is_url("http://www.example.com", strict=True) is True
        
        # URLs that might pass basic parsing but fail strict regex
        # Note: This depends on the actual URL_REGEX implementation
        assert is_url("https://example.com/path", strict=True) is True


class TestCleanString:
    """Test cases for clean_string function."""

    def test_clean_string_with_normal_text(self):
        """Test clean_string with normal text."""
        assert clean_string("Hello World") == "Hello World"
        assert clean_string("Simple text") == "Simple text"

    def test_clean_string_with_whitespace(self):
        """Test clean_string with various whitespace characters."""
        assert clean_string("  Hello   World  ") == "Hello World"
        assert clean_string("\tHello\tWorld\t") == "Hello World"
        assert clean_string("Hello\n\nWorld") == "Hello World"
        assert clean_string("Hello\r\nWorld") == "Hello World"

    def test_clean_string_with_mixed_whitespace(self):
        """Test clean_string with mixed whitespace and newlines."""
        assert clean_string("  Hello\n  \r\n  World  \t") == "Hello World"
        assert clean_string("Line1\nLine2\rLine3") == "Line1 Line2 Line3"

    def test_clean_string_with_empty_and_none(self):
        """Test clean_string with empty strings and None."""
        assert clean_string("") is None
        assert clean_string(None) is None
        assert clean_string("   ") == ""  # Only whitespace returns empty string

    def test_clean_string_with_single_words(self):
        """Test clean_string with single words."""
        assert clean_string("  word  ") == "word"
        assert clean_string("\tword\n") == "word"

    def test_clean_string_with_special_characters(self):
        """Test clean_string with special characters."""
        assert clean_string("Hello, World!") == "Hello, World!"
        assert clean_string("Test@123") == "Test@123"
        assert clean_string("Price: $100") == "Price: $100"

    def test_clean_string_with_multiple_spaces(self):
        """Test clean_string with multiple consecutive spaces."""
        assert clean_string("Hello     World") == "Hello World"
        assert clean_string("A  B  C  D") == "A B C D"
