"""Unit tests for application utility functions."""

import pytest
import pandas as pd
from pathlib import Path
from unittest.mock import patch, Mock
from datetime import datetime, UTC

from taskify.application.utils import clean_list_fields, convert_to_csv


class TestCleanListFields:
    """Test cases for clean_list_fields function."""

    def test_clean_list_fields_with_list_values(self):
        """Test clean_list_fields with data containing list values."""
        data = [
            {"name": "Hotel A", "issues": ["issue1", "issue2", "issue3"]},
            {"name": "Hotel B", "issues": ["issue4", "issue5"]},
            {"name": "Hotel C", "issues": []}
        ]
        
        result = clean_list_fields(data)
        
        assert result[0]["issues"] == "issue1, issue2, issue3"
        assert result[1]["issues"] == "issue4, issue5"
        assert result[2]["issues"] == ""

    def test_clean_list_fields_with_mixed_data_types(self):
        """Test clean_list_fields with mixed data types."""
        data = [
            {
                "name": "Hotel A",
                "issues": ["issue1", "issue2"],
                "rating": 4.5,
                "tags": ["tag1", "tag2", "tag3"],
                "active": True
            }
        ]
        
        result = clean_list_fields(data)
        
        assert result[0]["issues"] == "issue1, issue2"
        assert result[0]["tags"] == "tag1, tag2, tag3"
        assert result[0]["rating"] == 4.5  # Non-list values unchanged
        assert result[0]["active"] is True  # Non-list values unchanged
        assert result[0]["name"] == "Hotel A"  # Non-list values unchanged

    def test_clean_list_fields_with_no_lists(self):
        """Test clean_list_fields with data containing no list values."""
        data = [
            {"name": "Hotel A", "rating": 4.5, "active": True},
            {"name": "Hotel B", "rating": 3.8, "active": False}
        ]
        
        result = clean_list_fields(data)
        
        # Data should remain unchanged
        assert result == data

    def test_clean_list_fields_with_empty_data(self):
        """Test clean_list_fields with empty data."""
        data = []
        result = clean_list_fields(data)
        assert result == []

    def test_clean_list_fields_with_nested_lists(self):
        """Test clean_list_fields with nested data structures."""
        data = [
            {
                "name": "Hotel A",
                "categories": ["luxury", "business"],
                "amenities": ["wifi", "pool", "gym"],
                "metadata": {"created": "2023-01-01", "tags": ["new", "featured"]}
            }
        ]
        
        result = clean_list_fields(data)
        
        assert result[0]["categories"] == "luxury, business"
        assert result[0]["amenities"] == "wifi, pool, gym"
        # Nested lists in dictionaries should not be processed
        assert result[0]["metadata"]["tags"] == ["new", "featured"]

    def test_clean_list_fields_modifies_original_data(self):
        """Test that clean_list_fields modifies the original data structure."""
        data = [{"issues": ["issue1", "issue2"]}]
        original_data = data.copy()
        
        result = clean_list_fields(data)
        
        # Function modifies the original data
        assert data[0]["issues"] == "issue1, issue2"
        assert result is data  # Returns the same object


class TestConvertToCsv:
    """Test cases for convert_to_csv function."""

    @patch('taskify.application.utils.get_settings')
    @patch('taskify.application.utils.datetime')
    def test_convert_to_csv_basic_functionality(self, mock_datetime, mock_get_settings, temp_directory):
        """Test basic functionality of convert_to_csv."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings
        
        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        
        data = [
            {"name": "Hotel A", "rating": 4.5},
            {"name": "Hotel B", "rating": 3.8}
        ]
        
        result_path = convert_to_csv(data, "test_file")
        
        # Check that file was created
        assert result_path.exists()
        assert result_path.suffix == ".csv"
        assert "test_file" in result_path.name
        assert "2023-01-01-10-30-00" in result_path.name
        
        # Check file content
        df = pd.read_csv(result_path)
        assert len(df) == 2
        assert "name" in df.columns
        assert "rating" in df.columns
        assert df.iloc[0]["name"] == "Hotel A"
        assert df.iloc[1]["name"] == "Hotel B"

    @patch('taskify.application.utils.get_settings')
    @patch('taskify.application.utils.datetime')
    @patch('taskify.application.utils.clean_list_fields')
    def test_convert_to_csv_with_response_flag(self, mock_clean_list_fields, mock_datetime, mock_get_settings, temp_directory):
        """Test convert_to_csv with response=True flag."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings
        
        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        
        mock_clean_list_fields.return_value = [{"name": "Hotel A", "issues": "issue1, issue2"}]
        
        data = [{"name": "Hotel A", "issues": ["issue1", "issue2"]}]
        
        result_path = convert_to_csv(data, "response_file", response=True)
        
        # Should call clean_list_fields when response=True
        mock_clean_list_fields.assert_called_once_with(data)
        
        # Check that file was created
        assert result_path.exists()

    @patch('taskify.application.utils.get_settings')
    @patch('taskify.application.utils.datetime')
    def test_convert_to_csv_without_response_flag(self, mock_datetime, mock_get_settings, temp_directory):
        """Test convert_to_csv with response=False (default)."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings
        
        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        
        data = [{"name": "Hotel A", "issues": ["issue1", "issue2"]}]
        
        with patch('taskify.application.utils.clean_list_fields') as mock_clean_list_fields:
            result_path = convert_to_csv(data, "input_file", response=False)
            
            # Should NOT call clean_list_fields when response=False
            mock_clean_list_fields.assert_not_called()
        
        # Check that file was created with original data
        assert result_path.exists()
        df = pd.read_csv(result_path)
        # List should be converted to string representation by pandas
        assert "issue1" in str(df.iloc[0]["issues"])

    def test_convert_to_csv_creates_directory(self, temp_directory):
        """Test that convert_to_csv creates the tmp directory if it doesn't exist."""
        # Test that the function works and creates files
        data = [{"name": "Hotel A"}]
        result_path = convert_to_csv(data, "test")

        # Should create a file
        assert result_path.exists()
        assert result_path.suffix == ".csv"
        assert "test" in result_path.name

        # Check file content
        df = pd.read_csv(result_path)
        assert len(df) == 1
        assert "name" in df.columns
        assert df.iloc[0]["name"] == "Hotel A"

    @patch('taskify.application.utils.get_settings')
    @patch('taskify.application.utils.datetime')
    def test_convert_to_csv_with_empty_data(self, mock_datetime, mock_get_settings, temp_directory):
        """Test convert_to_csv with empty data."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings

        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        mock_datetime.UTC = UTC  # Add UTC to the mock

        data = []
        result_path = convert_to_csv(data, "empty_file")

        # Should create file even with empty data
        assert result_path.exists()
        # Empty DataFrame creates a CSV with no columns, so we just check the file exists
        # and has minimal content (just the header line or empty)
        with open(result_path, 'r') as f:
            content = f.read()
            assert len(content) >= 0  # File exists and can be read

    @patch('taskify.application.utils.get_settings')
    @patch('taskify.application.utils.datetime')
    def test_convert_to_csv_filename_format(self, mock_datetime, mock_get_settings, temp_directory):
        """Test that convert_to_csv creates correctly formatted filename."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings
        
        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        
        data = [{"test": "data"}]
        result_path = convert_to_csv(data, "my_file")
        
        # Check filename format: YYYY-MM-DD-HH-MM-SS-filename.csv
        expected_filename = "2023-01-01-10-30-00-my_file.csv"
        assert result_path.name == expected_filename

    @patch('taskify.application.utils.get_settings')
    def test_convert_to_csv_uses_utc_time(self, mock_get_settings, temp_directory):
        """Test that convert_to_csv uses UTC time."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.ROOT_DIR = temp_directory
        mock_get_settings.return_value = mock_settings
        
        data = [{"test": "data"}]
        
        with patch('taskify.application.utils.datetime') as mock_datetime:
            mock_now = Mock()
            mock_datetime.now.return_value = mock_now
            
            convert_to_csv(data, "test_file")
            
            # Should call datetime.now with UTC
            mock_datetime.now.assert_called_once_with(UTC)
