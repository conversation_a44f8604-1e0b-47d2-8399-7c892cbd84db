"""Unit tests for ProcessData<PERSON><PERSON>ler command handler."""

import pytest
import pandas as pd
from datetime import date, datetime, UTC
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from taskify.application.command_handlers.process_data import ProcessDataHandler
from taskify.api.request.payload import ProcessDataRequest


class TestProcessData<PERSON>andler:
    """Test cases for ProcessDataHandler class."""

    @pytest.fixture
    def process_data_handler(self, mock_aws_client, mock_ai_client, mock_email_client):
        """Create a ProcessDataHandler instance with mocked dependencies."""
        mock_aom_processing = Mock()
        mock_guest_processing = Mock()
        return ProcessDataHandler(
            aws_client=mock_aws_client,
            ai_client=mock_ai_client,
            email_client=mock_email_client,
            aom_processing=mock_aom_processing,
            guest_processing=mock_guest_processing
        )

    def test_handle_with_async_true(self, process_data_handler, sample_process_data_request):
        """Test handle method with async=True."""
        with patch('taskify.application.command_handlers.process_data.process_data_task') as mock_task:
            mock_task.apply_async = Mock()
            
            process_data_handler.handle(sample_process_data_request, run_async=True)
            
            mock_task.apply_async.assert_called_once_with(kwargs={"data": sample_process_data_request})

    def test_handle_with_async_false(self, process_data_handler, sample_process_data_request):
        """Test handle method with async=False."""
        with patch.object(process_data_handler, 'handle_process_data') as mock_handle:
            process_data_handler.handle(sample_process_data_request, run_async=False)
            
            mock_handle.assert_called_once_with(sample_process_data_request)

    @patch('taskify.application.command_handlers.process_data.convert_to_csv')
    @patch('taskify.application.command_handlers.process_data.datetime')
    def test_handle_process_data_complete_flow(self, mock_datetime, mock_convert_to_csv, process_data_handler, sample_process_data_request, mock_settings):
        """Test complete flow of handle_process_data method."""
        # Setup mocks
        mock_now = Mock()
        mock_now.strftime.return_value = "2023-01-01-10-30-00"
        mock_datetime.now.return_value = mock_now
        
        mock_convert_to_csv.side_effect = [
            Path("/tmp/input.csv"),  # First call for input file
            Path("/tmp/output.csv")  # Second call for output file
        ]
        
        # Mock get_issue_list to return some issues
        with patch.object(process_data_handler, 'get_issue_list') as mock_get_issues:
            mock_get_issues.return_value = [
                {"issue": "Test issue", "hotel_name": "Hotel A"}
            ]
            
            with patch('taskify.application.command_handlers.process_data.get_settings') as mock_get_settings:
                mock_get_settings.return_value = mock_settings
                
                process_data_handler.handle_process_data(sample_process_data_request)
        
        # Verify the flow
        mock_get_issues.assert_called_once()
        assert mock_convert_to_csv.call_count == 2
        process_data_handler.aws_client.upload_file_to_s3.assert_called()
        process_data_handler.ai_client.get_ai_response.assert_called_once()
        process_data_handler.email_client.send_email.assert_called_once()

    def test_handle_process_data_with_limit(self, process_data_handler):
        """Test handle_process_data with limit parameter."""
        request = ProcessDataRequest(
            process_prowl=True,
            process_press9=True,
            date=date(2023, 1, 1),
            limit=50
        )
        
        with patch.object(process_data_handler, 'get_issue_list') as mock_get_issues:
            mock_get_issues.return_value = [{"issue": f"Issue {i}", "hotel_name": "Hotel A"} for i in range(100)]
            
            with patch.object(process_data_handler, 'shuffle_list_with_limit') as mock_shuffle:
                mock_shuffle.return_value = [{"issue": "Limited issue", "hotel_name": "Hotel A"}]
                
                with patch('taskify.application.command_handlers.process_data.convert_to_csv'):
                    with patch('taskify.application.command_handlers.process_data.get_settings'):
                        process_data_handler.handle_process_data(request)
                
                mock_shuffle.assert_called_once_with(mock_get_issues.return_value, 50)

    def test_get_issue_list_with_both_sources(self, process_data_handler, mock_settings):
        """Test get_issue_list with both prowl and press9 enabled."""
        test_date = date(2023, 1, 1)
        
        # Setup AWS client mocks
        process_data_handler.aws_client.folder_exists.return_value = True
        process_data_handler.aws_client.list_files_in_folder.return_value = ["file1.csv", "file2.csv"]
        process_data_handler.aws_client.read_aws_file.return_value = "hotel,review\nHotel A,Great service"
        
        with patch('taskify.application.command_handlers.process_data.get_settings') as mock_get_settings:
            mock_get_settings.return_value = mock_settings
            
            with patch('pandas.read_csv') as mock_read_csv:
                mock_df = Mock()
                mock_read_csv.return_value = mock_df
                
                # Mock processing methods
                process_data_handler.guest_processing.process.return_value = [{"issue": "Guest issue", "hotel_name": "Hotel A"}]
                process_data_handler.aom_processing.process.return_value = [{"issue": "AOM issue", "hotel_name": "Hotel B"}]
                
                result = process_data_handler.get_issue_list(
                    date=test_date,
                    process_prowl=True,
                    process_press9=True
                )
        
        # Should process both sources
        assert len(result) > 0
        process_data_handler.guest_processing.process.assert_called()
        process_data_handler.aom_processing.process.assert_called()

    def test_get_issue_list_with_prowl_only(self, process_data_handler, mock_settings):
        """Test get_issue_list with only prowl enabled."""
        test_date = date(2023, 1, 1)
        
        process_data_handler.aws_client.folder_exists.return_value = True
        process_data_handler.aws_client.list_files_in_folder.return_value = ["file1.csv"]
        process_data_handler.aws_client.read_aws_file.return_value = "hotel,review\nHotel A,Great service"
        
        with patch('taskify.application.command_handlers.process_data.get_settings') as mock_get_settings:
            mock_get_settings.return_value = mock_settings
            
            with patch('pandas.read_csv') as mock_read_csv:
                mock_df = Mock()
                mock_read_csv.return_value = mock_df
                
                process_data_handler.guest_processing.process.return_value = [{"issue": "Guest issue", "hotel_name": "Hotel A"}]
                
                result = process_data_handler.get_issue_list(
                    date=test_date,
                    process_prowl=True,
                    process_press9=False
                )
        
        # Should only process prowl
        process_data_handler.guest_processing.process.assert_called()
        process_data_handler.aom_processing.process.assert_not_called()

    def test_get_issue_list_with_nonexistent_folder(self, process_data_handler):
        """Test get_issue_list when date folder doesn't exist."""
        test_date = date(2023, 1, 1)
        
        process_data_handler.aws_client.folder_exists.return_value = False
        
        result = process_data_handler.get_issue_list(
            date=test_date,
            process_prowl=True,
            process_press9=True
        )
        
        assert result == []

    def test_get_issue_list_with_nonexistent_source_folder(self, process_data_handler, mock_settings):
        """Test get_issue_list when source folder doesn't exist."""
        test_date = date(2023, 1, 1)
        
        # Main date folder exists, but source folders don't
        def folder_exists_side_effect(prefix):
            if prefix == "2023-01-01":
                return True
            return False
        
        process_data_handler.aws_client.folder_exists.side_effect = folder_exists_side_effect
        
        with patch('taskify.application.command_handlers.process_data.get_settings') as mock_get_settings:
            mock_get_settings.return_value = mock_settings
            
            result = process_data_handler.get_issue_list(
                date=test_date,
                process_prowl=True,
                process_press9=True
            )
        
        assert result == []

    def test_shuffle_list_with_limit_normal_case(self, process_data_handler):
        """Test shuffle_list_with_limit with normal input."""
        input_list = [f"item_{i}" for i in range(100)]
        limit = 50
        
        result = process_data_handler.shuffle_list_with_limit(input_list, limit)
        
        assert len(result) == limit
        assert all(item in input_list for item in result)

    def test_shuffle_list_with_limit_smaller_list(self, process_data_handler):
        """Test shuffle_list_with_limit when input is smaller than limit."""
        input_list = ["item_1", "item_2", "item_3"]
        limit = 50
        
        result = process_data_handler.shuffle_list_with_limit(input_list, limit)
        
        assert len(result) == 3  # Should return all items
        assert set(result) == set(input_list)

    def test_shuffle_list_with_limit_empty_list(self, process_data_handler):
        """Test shuffle_list_with_limit with empty input."""
        input_list = []
        limit = 50
        
        result = process_data_handler.shuffle_list_with_limit(input_list, limit)
        
        assert result == []

    def test_shuffle_list_with_limit_zero_limit(self, process_data_handler):
        """Test shuffle_list_with_limit with zero limit."""
        input_list = ["item_1", "item_2", "item_3"]
        limit = 0
        
        result = process_data_handler.shuffle_list_with_limit(input_list, limit)
        
        assert result == []

    @patch('taskify.application.command_handlers.process_data.io.StringIO')
    @patch('pandas.read_csv')
    def test_get_issue_list_csv_processing(self, mock_read_csv, mock_string_io, process_data_handler, mock_settings):
        """Test that get_issue_list properly processes CSV data."""
        test_date = date(2023, 1, 1)
        csv_data = "hotel,review\nHotel A,Great service\nHotel B,Poor service"
        
        process_data_handler.aws_client.folder_exists.return_value = True
        process_data_handler.aws_client.list_files_in_folder.return_value = ["file1.csv"]
        process_data_handler.aws_client.read_aws_file.return_value = csv_data
        
        mock_df = pd.DataFrame({
            'hotel': ['Hotel A', 'Hotel B'],
            'review': ['Great service', 'Poor service']
        })
        mock_read_csv.return_value = mock_df
        
        with patch('taskify.application.command_handlers.process_data.get_settings') as mock_get_settings:
            mock_get_settings.return_value = mock_settings
            
            process_data_handler.guest_processing.process.return_value = [
                {"issue": "Great service", "hotel_name": "Hotel A"}
            ]
            
            result = process_data_handler.get_issue_list(
                date=test_date,
                process_prowl=True,
                process_press9=False
            )
        
        # Verify CSV was processed correctly
        mock_string_io.assert_called_with(csv_data)
        mock_read_csv.assert_called()
        process_data_handler.guest_processing.process.assert_called_with(mock_df)

    @patch('taskify.application.command_handlers.process_data.EmailCredentials')
    def test_handle_process_data_email_sending(self, mock_email_credentials, process_data_handler, sample_process_data_request):
        """Test that handle_process_data sends email with correct parameters."""
        with patch.object(process_data_handler, 'get_issue_list') as mock_get_issues:
            mock_get_issues.return_value = [{"issue": "Test issue", "hotel_name": "Hotel A"}]

            with patch('taskify.application.command_handlers.process_data.convert_to_csv') as mock_convert:
                mock_convert.side_effect = [Path("/tmp/input.csv"), Path("/tmp/output.csv")]

                process_data_handler.handle_process_data(sample_process_data_request)

        # Verify email credentials were created (with actual settings values)
        mock_email_credentials.assert_called_once()

        # Verify email was sent
        process_data_handler.email_client.send_email.assert_called_once()
        call_args = process_data_handler.email_client.send_email.call_args
        assert call_args[1]['is_html'] is True

    def test_initialization_with_dependencies(self, mock_aws_client, mock_ai_client, mock_email_client):
        """Test ProcessDataHandler initialization with all dependencies."""
        mock_aom_processing = Mock()
        mock_guest_processing = Mock()

        handler = ProcessDataHandler(
            aws_client=mock_aws_client,
            ai_client=mock_ai_client,
            email_client=mock_email_client,
            aom_processing=mock_aom_processing,
            guest_processing=mock_guest_processing
        )

        assert handler.aws_client == mock_aws_client
        assert handler.ai_client == mock_ai_client
        assert handler.email_client == mock_email_client
        assert handler.aom_processing == mock_aom_processing
        assert handler.guest_processing == mock_guest_processing
