"""Pytest configuration and shared fixtures."""

import pandas as pd
import pytest
from datetime import date, datetime
from unittest.mock import Mo<PERSON>, MagicMock
from pathlib import Path
import tempfile
import os

from taskify.api.request.payload import ProcessDataRequest
from taskify.domain.aom import AOMProcessing
from taskify.domain.guest import GuestProcessing


@pytest.fixture
def sample_aom_dataframe():
    """Create a sample AOM DataFrame for testing."""
    return pd.DataFrame({
        'Date': ['2023-01-01', '2023-01-02'],
        'Site name': ['Hotel A', 'Hotel B'],
        'Name': ['<PERSON>', '<PERSON>'],
        'Email address': ['<EMAIL>', '<EMAIL>'],
        'Phone no.': ['1234567890', '0987654321'],
        'Location': ['City A', 'City B'],
        'Created At': ['2023-01-01 10:00:00', '2023-01-02 11:00:00'],
        'Issue Type': ['Cleanliness', 'Maintenance'],
        'Issue Description': ['Room dirty', 'AC not working'],
        'Severity': ['High', 'Medium'],
        'Status': ['Open', 'Closed'],
        'Invalid Field': ['invalid_value', 'another_invalid'],
        'URL Field': ['https://example.com', 'not_a_url'],
        'Number Field': ['123', 'not_a_number'],
        'Date Field': ['2023-01-01', 'invalid_date']
    })


@pytest.fixture
def sample_guest_dataframe():
    """Create a sample guest DataFrame for testing."""
    return pd.DataFrame({
        'hotel': ['Hotel A', 'Hotel B', 'Hotel A', 'Hotel C'],
        'review': ['Great service', 'Poor cleanliness', 'Great service', 'Average experience'],
        'rating': [5, 2, 5, 3],
        'date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04']
    })


@pytest.fixture
def sample_process_data_request():
    """Create a sample ProcessDataRequest for testing."""
    return ProcessDataRequest(
        process_prowl=True,
        process_press9=True,
        date=date(2023, 1, 1),
        limit=100
    )


@pytest.fixture
def mock_aws_client():
    """Create a mock AWS client."""
    mock = Mock()
    mock.folder_exists.return_value = True
    mock.list_files_in_folder.return_value = ['file1.csv', 'file2.csv']
    mock.read_aws_file.return_value = "hotel,review\nHotel A,Great service"
    mock.upload_file_to_s3.return_value = "https://s3.amazonaws.com/bucket/file.csv"
    return mock


@pytest.fixture
def mock_ai_client():
    """Create a mock AI client."""
    mock = Mock()
    mock.get_ai_response.return_value = [
        {"hotel_name": "Hotel A", "theme": ["cleanliness"], "HSE": ["Action 1"], "MSE": [], "LSE": []}
    ]
    return mock


@pytest.fixture
def mock_email_client():
    """Create a mock email client."""
    mock = Mock()
    mock.send_email.return_value = None
    return mock


@pytest.fixture
def aom_processing():
    """Create an AOMProcessing instance."""
    return AOMProcessing()


@pytest.fixture
def guest_processing():
    """Create a GuestProcessing instance."""
    return GuestProcessing()


@pytest.fixture
def temp_directory():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_settings():
    """Create mock settings."""
    mock = Mock()
    mock.ROOT_DIR = Path("/tmp")
    mock.SOURCE_PROWL_KEY = "prowl"
    mock.SOURCE_PRESS9_KEY = "press9"
    mock.EMAIL_SENDER = "<EMAIL>"
    mock.EMAIL_SENDER_PASSWORD = "password"
    mock.EMAIL_RECIPIENTS = ["<EMAIL>"]
    return mock


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_directory):
    """Set up test environment variables."""
    monkeypatch.setenv("APP_ENV", "QA")
    monkeypatch.setenv("AWS_REGION", "us-east-1")
    monkeypatch.setenv("AWS_BUCKET_NAME", "test-bucket")
