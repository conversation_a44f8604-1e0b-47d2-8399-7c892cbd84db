#!/usr/bin/env python3
"""Test runner script for the audit-review-processor project."""

import subprocess
import sys
from pathlib import Path


def run_tests():
    """Run the test suite with pytest."""
    print("🧪 Running unit tests for audit-review-processor...")
    print("=" * 60)
    
    # Ensure we're in the project root
    project_root = Path(__file__).parent
    
    # Run pytest with coverage
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=taskify",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov",
        "--cov-fail-under=80"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        
        if result.returncode == 0:
            print("\n✅ All tests passed!")
            print("📊 Coverage report generated in htmlcov/index.html")
        else:
            print("\n❌ Some tests failed or coverage is below threshold.")
            print("📊 Check the coverage report in htmlcov/index.html")
            
        return result.returncode
        
    except Exception as e:
        print(f"\n💥 Error running tests: {e}")
        return 1


def run_specific_tests(test_path: str):
    """Run specific tests."""
    print(f"🧪 Running tests for: {test_path}")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    cmd = [
        sys.executable, "-m", "pytest",
        test_path,
        "-v",
        "--tb=short"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode
    except Exception as e:
        print(f"\n💥 Error running tests: {e}")
        return 1


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Run specific test file or directory
        test_path = sys.argv[1]
        exit_code = run_specific_tests(test_path)
    else:
        # Run all tests
        exit_code = run_tests()
    
    sys.exit(exit_code)
